"""
Test script for IAF-FBO Python implementation
"""

import numpy as np
import sys
import os

# Add pyiaf to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'pyiaf'))

from pyiaf import IAF_FBO


def test_basic_functionality():
    """Test basic functionality of IAF-FBO"""
    print("Testing basic IAF-FBO functionality...")
    
    # Simple objective functions
    def sphere(x):
        return np.sum(x**2)
    
    def shifted_sphere(x):
        return np.sum((x - 1)**2)
    
    # Problem setup
    bounds = (np.array([-2.0, -2.0]), np.array([2.0, 2.0]))
    objective_functions = {0: sphere, 1: shifted_sphere}
    
    # Create optimizer with minimal settings for quick test
    optimizer = IAF_FBO(
        n_clients=2,
        bounds=bounds,
        n_initial=10,  # Small for quick test
        max_iterations=5,  # Small for quick test
        af_type='LCB',
        n_clusters=2,
        pop_size=20,  # Small for quick test
        cso_iters=10,  # Small for quick test
        random_state=42
    )
    
    # Setup and run
    optimizer.setup_clients(objective_functions)
    results = optimizer.run_optimization()
    
    # Verify results structure
    assert 'best_solutions' in results
    assert 'best_values' in results
    assert 'history' in results
    
    # Verify we have results for both clients
    assert 0 in results['best_solutions']
    assert 1 in results['best_solutions']
    
    print("✓ Basic functionality test passed!")
    return results


def test_components():
    """Test individual components"""
    print("Testing individual components...")
    
    # Test Gaussian Process
    from pyiaf.gaussian_process import GaussianProcess
    
    X = np.random.randn(10, 2)
    y = np.sum(X**2, axis=1)
    
    gp = GaussianProcess(random_state=42)
    gp.fit(X, y)
    
    X_test = np.random.randn(5, 2)
    mu, sigma = gp.predict(X_test, return_std=True)
    
    assert len(mu) == 5
    assert len(sigma) == 5
    assert np.all(sigma >= 0)
    
    print("✓ Gaussian Process test passed!")
    
    # Test Neural Classifier
    from pyiaf.neural_classifier import NeuralClassifier
    from pyiaf.utils import create_pairwise_data
    
    X = np.random.randn(20, 4)
    y = np.random.randn(20)
    
    X_pairs, y_pairs = create_pairwise_data(X, y)
    
    classifier = NeuralClassifier(random_state=42)
    classifier.fit(X_pairs, y_pairs)
    
    predictions = classifier.predict(X_pairs[:5])
    assert len(predictions) == 5
    assert np.all(np.isin(predictions, [-1, 0, 1]))
    
    print("✓ Neural Classifier test passed!")
    
    # Test CSO
    from pyiaf.competitive_swarm_optimizer import CompetitiveSwarmOptimizer
    
    bounds = (np.array([-1, -1]), np.array([1, 1]))
    cso = CompetitiveSwarmOptimizer(pop_size=10, max_iters=5, random_state=42)
    
    # Create a dummy classifier for testing
    dummy_classifier = NeuralClassifier(random_state=42)
    dummy_X_pairs = np.random.randn(50, 4)
    dummy_y_pairs = np.random.choice([-1, 0, 1], 50)
    dummy_classifier.fit(dummy_X_pairs, dummy_y_pairs)
    
    best_solution, _ = cso.optimize(dummy_classifier, bounds)
    
    assert len(best_solution) == 2
    assert np.all(best_solution >= bounds[0])
    assert np.all(best_solution <= bounds[1])
    
    print("✓ CSO test passed!")
    
    # Test Federated Framework
    from pyiaf.federated_framework import FederatedServer, FederatedClient
    
    server = FederatedServer(n_clusters=2, random_state=42)
    
    client1 = FederatedClient(0, bounds, random_state=42)
    client2 = FederatedClient(1, bounds, random_state=43)
    
    server.register_client(client1)
    server.register_client(client2)
    
    # Add some dummy data
    client1.update_data(np.array([0.5, 0.5]), 0.5)
    client2.update_data(np.array([-0.5, -0.5]), 0.25)
    
    groups = server.cluster_clients()
    assert len(groups) <= 2
    
    print("✓ Federated Framework test passed!")


def test_utilities():
    """Test utility functions"""
    print("Testing utility functions...")
    
    from pyiaf.utils import (
        lhs_classic, rbf_kernel, acquisition_function,
        normalize_data, create_pairwise_data, kmeans_clustering
    )
    
    # Test LHS
    samples = lhs_classic(10, 3, random_state=42)
    assert samples.shape == (10, 3)
    assert np.all(samples >= 0) and np.all(samples <= 1)
    
    # Test RBF kernel
    x1 = np.array([[0, 0], [1, 1]])
    x2 = np.array([[0, 0], [0.5, 0.5]])
    theta = np.array([1.0, 1.0])
    K = rbf_kernel(x1, x2, theta)
    assert K.shape == (2, 2)
    
    # Test acquisition function
    mu = np.array([0.5, 1.0, -0.5])
    sigma = np.array([0.1, 0.2, 0.15])
    af_vals = acquisition_function(mu, sigma, 0.0, 'LCB')
    assert len(af_vals) == 3
    
    # Test normalization
    X = np.random.randn(10, 2)
    bounds = (np.array([-1, -1]), np.array([1, 1]))
    X_norm, _ = normalize_data(X, bounds)
    # Note: X might be outside bounds, so we don't check bounds here
    
    # Test pairwise data creation
    X = np.random.randn(5, 2)
    y = np.random.randn(5)
    X_pairs, y_pairs = create_pairwise_data(X, y)
    assert X_pairs.shape[0] == 10  # 5 choose 2 = 10 pairs
    assert X_pairs.shape[1] == 4   # 2 * 2 dimensions
    
    # Test k-means
    data = np.random.randn(20, 3)
    labels, centroids = kmeans_clustering(data, 3, random_state=42)
    assert len(labels) == 20
    assert centroids.shape == (3, 3)
    
    print("✓ Utility functions test passed!")


def run_all_tests():
    """Run all tests"""
    print("Running IAF-FBO Python implementation tests...")
    print("=" * 50)
    
    try:
        # Test components first
        test_components()
        print()
        
        # Test utilities
        test_utilities()
        print()
        
        # Test basic functionality
        results = test_basic_functionality()
        print()
        
        # Print sample results
        print("Sample optimization results:")
        for client_id in results['best_solutions']:
            print(f"  Client {client_id}: {results['best_values'][client_id]:.6f}")
        
        print("\n" + "=" * 50)
        print("✅ All tests passed successfully!")
        print("IAF-FBO Python implementation is working correctly.")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_all_tests()
    
    if success:
        print("\n🎉 Ready to use IAF-FBO!")
        print("You can now run the examples in pyiaf/example.py")
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")
        sys.exit(1)
