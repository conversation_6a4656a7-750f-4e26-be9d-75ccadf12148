# PyIAF: Python Implementation of Implicit Acquisition Function for Federated Bayesian Optimization

This package implements the IAF-FBO algorithm as described in the paper:
**"Optimization of an Implicit Acquisition Function for Federated Bayesian Many-Task Optimization"**

## Overview

IAF-FBO is a federated Bayesian optimization algorithm that enables multiple clients to collaboratively optimize their individual tasks while preserving data privacy. The key innovation is the use of implicit acquisition functions optimized through pairwise comparisons learned by neural network classifiers.

## Key Features

- **Privacy-Preserving**: Clients only share model parameters, not raw data
- **Federated Learning**: Supports multiple clients with different optimization tasks
- **Implicit Acquisition Functions**: Uses neural classifiers for pairwise comparisons
- **Knowledge Transfer**: Enables knowledge sharing between similar tasks
- **Competitive Swarm Optimization**: Optimizes acquisition functions without explicit construction

## Algorithm Components

1. **Gaussian Process**: Surrogate model for each client's objective function
2. **Neural Classifier**: Learns pairwise preferences from acquisition function values
3. **Competitive Swarm Optimizer (CSO)**: Optimizes implicit acquisition functions
4. **Federated Framework**: Manages client clustering and model aggregation

## Installation

```bash
# Install required dependencies
pip install numpy scipy scikit-learn matplotlib

# The package is self-contained in the pyiaf directory
```

## Quick Start

```python
import numpy as np
from pyiaf import IAF_FBO

# Define objective functions for each client
def objective_1(x):
    return np.sum(x**2)  # Sphere function

def objective_2(x):
    return np.sum((x - 1)**2)  # Shifted sphere function

# Define problem bounds
bounds = (np.array([-5, -5]), np.array([5, 5]))  # 2D problem

# Create IAF-FBO instance
optimizer = IAF_FBO(
    n_clients=2,
    bounds=bounds,
    n_initial=20,
    max_iterations=30,
    af_type='LCB',
    random_state=42
)

# Setup clients with objective functions
objective_functions = {
    0: objective_1,
    1: objective_2
}
optimizer.setup_clients(objective_functions)

# Run optimization
results = optimizer.run_optimization()

# Print results
for client_id in results['best_solutions']:
    print(f"Client {client_id}:")
    print(f"  Best solution: {results['best_solutions'][client_id]}")
    print(f"  Best value: {results['best_values'][client_id]:.6f}")
```

## Parameters

### IAF_FBO Parameters

- `n_clients`: Number of federated clients
- `bounds`: Problem bounds (lower, upper) for each client
- `n_initial`: Number of initial samples per client (default: 50)
- `max_iterations`: Maximum number of optimization iterations (default: 60)
- `af_type`: Acquisition function type ('UCB', 'LCB', 'EI') (default: 'LCB')
- `n_clusters`: Number of clusters for client grouping (default: 6)
- `pop_size`: Population size for CSO (default: 100)
- `cso_iters`: Number of CSO iterations (default: 100)
- `transfer_prob`: Probability of using global classifier (default: 0.5)
- `noise_prob`: Probability of label noise in classifier training (default: 0.0)
- `random_state`: Random seed for reproducibility

## Examples

### Multi-Client Example

```python
from pyiaf import IAF_FBO
import numpy as np

# Define different objective functions
def sphere(x):
    return np.sum(x**2)

def rosenbrock(x):
    result = 0
    for i in range(len(x) - 1):
        result += 100 * (x[i+1] - x[i]**2)**2 + (1 - x[i])**2
    return result

def ackley(x):
    a, b, c = 20, 0.2, 2*np.pi
    d = len(x)
    sum1 = np.sum(x**2)
    sum2 = np.sum(np.cos(c * x))
    return -a * np.exp(-b * np.sqrt(sum1/d)) - np.exp(sum2/d) + a + np.e

# Setup optimization
objective_functions = {
    0: sphere,
    1: rosenbrock,
    2: ackley
}

bounds = (np.array([-2.0, -2.0]), np.array([2.0, 2.0]))

optimizer = IAF_FBO(
    n_clients=3,
    bounds=bounds,
    n_initial=15,
    max_iterations=25,
    n_clusters=2,
    transfer_prob=0.7
)

optimizer.setup_clients(objective_functions)
results = optimizer.run_optimization()
```

### Running Examples

```python
# Run the provided examples
from pyiaf.example import run_simple_example, run_multi_client_example

# Simple 2-client example
results1 = run_simple_example()

# Multi-client example with different functions
results2 = run_multi_client_example()
```

## Architecture

### Core Classes

- **IAF_FBO**: Main algorithm class
- **GaussianProcess**: Gaussian process surrogate model
- **NeuralClassifier**: Neural network for pairwise comparisons
- **CompetitiveSwarmOptimizer**: CSO for optimizing implicit acquisition functions
- **FederatedServer**: Manages client clustering and model aggregation
- **FederatedClient**: Represents individual optimization tasks

### Algorithm Flow

1. **Initialization**: Each client generates initial samples using Latin Hypercube Sampling
2. **Local Training**: Clients train Gaussian processes and neural classifiers
3. **Clustering**: Server clusters clients based on classifier weight similarity
4. **Aggregation**: Server aggregates classifiers within each cluster
5. **Optimization**: Clients optimize acquisition functions using CSO
6. **Evaluation**: New points are evaluated and added to client datasets
7. **Iteration**: Process repeats until convergence or maximum iterations

## Comparison with MATLAB Implementation

This Python implementation maintains logical consistency with the original MATLAB version while providing:

- **Improved Modularity**: Clear separation of components
- **Better Documentation**: Comprehensive docstrings and examples
- **Enhanced Flexibility**: Configurable parameters and extensible design
- **Python Ecosystem**: Integration with NumPy, SciPy, and scikit-learn

## Dependencies

- NumPy: Numerical computations
- SciPy: Optimization and statistical functions
- scikit-learn: Neural network implementation
- Matplotlib: Plotting (optional)

## Citation

If you use this implementation, please cite the original paper:

```
@article{liu2025iaf,
  title={Optimization of an Implicit Acquisition Function for Federated Bayesian Many-Task Optimization},
  author={Liu, Qiqi and Jin, Yaochu and Chen, Guodong},
  journal={IEEE Transactions on Evolutionary Computation},
  year={2025}
}
```

## License

This implementation is provided for research and educational purposes.
