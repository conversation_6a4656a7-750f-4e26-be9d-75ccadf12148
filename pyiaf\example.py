"""
Example usage of IAF-FBO algorithm
"""

import numpy as np
import sys
import os

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import matplotlib.pyplot as plt
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False

from pyiaf.iaf_fbo import IAF_FBO


def sphere_function(x):
    """Sphere function: f(x) = sum(x_i^2)"""
    return np.sum(x**2)


def shifted_sphere_function(x, shift=1.0):
    """Shifted sphere function: f(x) = sum((x_i - shift)^2)"""
    return np.sum((x - shift)**2)


def rosenbrock_function(x):
    """Rosenbrock function: f(x) = sum(100*(x_{i+1} - x_i^2)^2 + (1 - x_i)^2)"""
    result = 0
    for i in range(len(x) - 1):
        result += 100 * (x[i+1] - x[i]**2)**2 + (1 - x[i])**2
    return result


def ackley_function(x):
    """Ackley function"""
    a, b, c = 20, 0.2, 2*np.pi
    d = len(x)
    sum1 = np.sum(x**2)
    sum2 = np.sum(np.cos(c * x))
    return -a * np.exp(-b * np.sqrt(sum1/d)) - np.exp(sum2/d) + a + np.e


def run_simple_example():
    """
    Run a simple example with 2 clients and 2D problems
    """
    print("Running simple IAF-FBO example...")
    print("=" * 50)
    
    # Define objective functions for each client
    objective_functions = {
        0: sphere_function,
        1: lambda x: shifted_sphere_function(x, shift=2.0)
    }
    
    # Define problem bounds (2D problem)
    bounds = (np.array([-5.0, -5.0]), np.array([5.0, 5.0]))
    
    # Create IAF-FBO instance
    optimizer = IAF_FBO(
        n_clients=2,
        bounds=bounds,
        n_initial=20,
        max_iterations=30,
        af_type='LCB',
        n_clusters=2,
        pop_size=50,
        cso_iters=50,
        random_state=42
    )
    
    # Setup clients
    optimizer.setup_clients(objective_functions)
    
    # Run optimization
    results = optimizer.run_optimization()
    
    # Print results
    print("\nOptimization Results:")
    print("-" * 30)
    for client_id in results['best_solutions']:
        print(f"Client {client_id}:")
        print(f"  Best solution: {results['best_solutions'][client_id]}")
        print(f"  Best value: {results['best_values'][client_id]:.6f}")
        print()
    
    return results


def run_multi_client_example():
    """
    Run an example with multiple clients and different functions
    """
    print("Running multi-client IAF-FBO example...")
    print("=" * 50)
    
    # Define objective functions for each client
    objective_functions = {
        0: sphere_function,
        1: lambda x: shifted_sphere_function(x, shift=1.5),
        2: rosenbrock_function,
        3: ackley_function
    }
    
    # Define problem bounds (2D problem)
    bounds = (np.array([-2.0, -2.0]), np.array([2.0, 2.0]))
    
    # Create IAF-FBO instance
    optimizer = IAF_FBO(
        n_clients=4,
        bounds=bounds,
        n_initial=15,
        max_iterations=25,
        af_type='LCB',
        n_clusters=2,
        pop_size=60,
        cso_iters=40,
        transfer_prob=0.7,
        random_state=123
    )
    
    # Setup clients
    optimizer.setup_clients(objective_functions)
    
    # Run optimization
    results = optimizer.run_optimization()
    
    # Print results
    print("\nOptimization Results:")
    print("-" * 30)
    function_names = ['Sphere', 'Shifted Sphere', 'Rosenbrock', 'Ackley']
    
    for client_id in results['best_solutions']:
        print(f"Client {client_id} ({function_names[client_id]}):")
        print(f"  Best solution: {results['best_solutions'][client_id]}")
        print(f"  Best value: {results['best_values'][client_id]:.6f}")
        print()
    
    return results


def plot_convergence(results, title="IAF-FBO Convergence"):
    """
    Plot convergence curves for each client

    Args:
        results: Results dictionary from IAF-FBO
        title: Plot title
    """
    if not HAS_MATPLOTLIB:
        print("Matplotlib not available. Cannot plot convergence curves.")
        return

    plt.figure(figsize=(10, 6))

    for client_id, client_data in results['history']['client_data'].items():
        best_y_history = client_data['best_y']
        iterations = range(len(best_y_history))
        plt.plot(iterations, best_y_history, 'o-', label=f'Client {client_id}')

    plt.xlabel('Iteration')
    plt.ylabel('Best Objective Value')
    plt.title(title)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.yscale('log')
    plt.show()


def compare_with_without_transfer():
    """
    Compare IAF-FBO performance with and without knowledge transfer
    """
    print("Comparing IAF-FBO with and without knowledge transfer...")
    print("=" * 60)
    
    # Define objective functions
    objective_functions = {
        0: sphere_function,
        1: lambda x: shifted_sphere_function(x, shift=1.0),
        2: lambda x: shifted_sphere_function(x, shift=2.0)
    }
    
    bounds = (np.array([-3.0, -3.0]), np.array([3.0, 3.0]))
    
    # Run with transfer
    print("Running with knowledge transfer (transfer_prob=0.8)...")
    optimizer_with_transfer = IAF_FBO(
        n_clients=3,
        bounds=bounds,
        n_initial=15,
        max_iterations=20,
        transfer_prob=0.8,
        random_state=42
    )
    optimizer_with_transfer.setup_clients(objective_functions)
    results_with_transfer = optimizer_with_transfer.run_optimization()
    
    # Run without transfer
    print("\nRunning without knowledge transfer (transfer_prob=0.0)...")
    optimizer_without_transfer = IAF_FBO(
        n_clients=3,
        bounds=bounds,
        n_initial=15,
        max_iterations=20,
        transfer_prob=0.0,
        random_state=42
    )
    optimizer_without_transfer.setup_clients(objective_functions)
    results_without_transfer = optimizer_without_transfer.run_optimization()
    
    # Compare results
    print("\nComparison Results:")
    print("-" * 40)
    print("With Transfer:")
    for client_id in results_with_transfer['best_values']:
        print(f"  Client {client_id}: {results_with_transfer['best_values'][client_id]:.6f}")
    
    print("\nWithout Transfer:")
    for client_id in results_without_transfer['best_values']:
        print(f"  Client {client_id}: {results_without_transfer['best_values'][client_id]:.6f}")
    
    return results_with_transfer, results_without_transfer


if __name__ == "__main__":
    # Run simple example
    results1 = run_simple_example()
    
    print("\n" + "="*60 + "\n")
    
    # Run multi-client example
    results2 = run_multi_client_example()
    
    print("\n" + "="*60 + "\n")
    
    # Compare with/without transfer
    results_with, results_without = compare_with_without_transfer()
    
    # Plot convergence if matplotlib is available
    try:
        plot_convergence(results1, "Simple Example Convergence")
        plot_convergence(results2, "Multi-Client Example Convergence")
    except:
        print("Could not generate plots.")
    
    print("\nAll examples completed successfully!")
