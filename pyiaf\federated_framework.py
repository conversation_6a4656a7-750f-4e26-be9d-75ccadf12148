"""
Federated Learning Framework for IAF-FBO algorithm
"""

import numpy as np
from .utils import kmeans_clustering
from .neural_classifier import aggregate_classifiers
import warnings
warnings.filterwarnings('ignore')


class FederatedClient:
    """
    Federated client for IAF-FBO
    """
    
    def __init__(self, client_id, bounds, initial_data=None, gp_model=None, 
                 classifier=None, random_state=None):
        """
        Initialize federated client
        
        Args:
            client_id: Unique client identifier
            bounds: Problem bounds (lower, upper)
            initial_data: Initial training data (X, y)
            gp_model: Gaussian Process model
            classifier: Neural classifier
            random_state: Random seed
        """
        self.client_id = client_id
        self.bounds = bounds
        self.random_state = random_state
        
        # Data
        if initial_data is not None:
            self.X_data, self.y_data = initial_data
        else:
            self.X_data = np.empty((0, len(bounds[0])))
            self.y_data = np.empty(0)
        
        # Models
        self.gp_model = gp_model
        self.classifier = classifier
        
        # History
        self.new_X = []
        self.new_y = []
        self.fail_count = 0
        
    def update_data(self, X_new, y_new):
        """
        Update client data with new observations

        Args:
            X_new: New input data
            y_new: New target values
        """
        X_new = np.asarray(X_new)
        y_new = np.asarray(y_new)

        if X_new.ndim == 1:
            X_new = X_new.reshape(1, -1)
        if y_new.ndim == 0:
            y_new = np.array([y_new])

        self.X_data = np.vstack([self.X_data, X_new])
        self.y_data = np.concatenate([self.y_data, y_new])

        # Update history
        self.new_X.append(X_new)
        self.new_y.append(y_new)

        # Update fail count
        if len(self.new_y) >= 2:
            if np.min(self.new_y[-1]) == np.min(self.new_y[-2]):
                self.fail_count += 1
            else:
                self.fail_count = 0
    
    def get_data(self):
        """
        Get client data
        
        Returns:
            X_data, y_data: Client's training data
        """
        return self.X_data.copy(), self.y_data.copy()
    
    def get_weight_vector(self):
        """
        Get classifier weight vector for similarity computation
        
        Returns:
            Weight vector
        """
        if self.classifier is not None and self.classifier.is_fitted:
            return self.classifier.get_weights()
        else:
            # Return random vector if classifier not fitted
            return np.random.randn(100)  # Placeholder size


class FederatedServer:
    """
    Federated server for IAF-FBO
    """
    
    def __init__(self, n_clusters=3, similarity_method='euclidean', random_state=None):
        """
        Initialize federated server
        
        Args:
            n_clusters: Number of clusters for client grouping
            similarity_method: Method for computing client similarity
            random_state: Random seed
        """
        self.n_clusters = n_clusters
        self.similarity_method = similarity_method
        self.random_state = random_state
        
        # Client management
        self.clients = {}
        self.client_groups = {}
        self.global_classifiers = {}
        
    def register_client(self, client):
        """
        Register a new client
        
        Args:
            client: FederatedClient instance
        """
        self.clients[client.client_id] = client
    
    def cluster_clients(self):
        """
        Cluster clients based on classifier weight similarity
        
        Returns:
            client_groups: Dictionary mapping group_id to list of client_ids
        """
        if len(self.clients) < self.n_clusters:
            # If fewer clients than clusters, each client is its own group
            self.client_groups = {i: [client_id] for i, client_id in enumerate(self.clients.keys())}
            return self.client_groups
        
        # Get weight vectors from all clients
        client_ids = list(self.clients.keys())
        weight_vectors = []
        
        for client_id in client_ids:
            weight_vector = self.clients[client_id].get_weight_vector()
            weight_vectors.append(weight_vector)
        
        weight_vectors = np.array(weight_vectors)
        
        # Perform clustering
        cluster_labels, _ = kmeans_clustering(
            weight_vectors, 
            self.n_clusters, 
            random_state=self.random_state
        )
        
        # Group clients by cluster
        self.client_groups = {}
        for i, client_id in enumerate(client_ids):
            group_id = cluster_labels[i]
            if group_id not in self.client_groups:
                self.client_groups[group_id] = []
            self.client_groups[group_id].append(client_id)
        
        return self.client_groups
    
    def aggregate_classifiers(self):
        """
        Aggregate classifiers within each client group
        
        Returns:
            global_classifiers: Dictionary mapping group_id to aggregated classifier
        """
        self.global_classifiers = {}
        
        for group_id, client_ids in self.client_groups.items():
            # Get classifiers from clients in this group
            classifiers = {}
            for client_id in client_ids:
                client = self.clients[client_id]
                if client.classifier is not None and client.classifier.is_fitted:
                    classifiers[client_id] = client.classifier
            
            if classifiers:
                # Aggregate classifiers
                agg_classifier = aggregate_classifiers(classifiers, list(classifiers.keys()))
                self.global_classifiers[group_id] = agg_classifier
        
        return self.global_classifiers
    
    def get_global_classifier(self, client_id):
        """
        Get global classifier for a specific client
        
        Args:
            client_id: Client identifier
        
        Returns:
            Global classifier for the client's group
        """
        # Find which group the client belongs to
        for group_id, client_ids in self.client_groups.items():
            if client_id in client_ids:
                return self.global_classifiers.get(group_id, None)
        
        return None
    
    def broadcast_classifiers(self):
        """
        Broadcast global classifiers to clients
        """
        for group_id, client_ids in self.client_groups.items():
            if group_id in self.global_classifiers:
                global_classifier = self.global_classifiers[group_id]
                
                for client_id in client_ids:
                    # Option 1: Replace client classifier with global one
                    # self.clients[client_id].classifier = global_classifier.copy()
                    
                    # Option 2: Keep local classifier but provide access to global one
                    # This is more flexible for the optimization phase
                    pass
    
    def get_client_group(self, client_id):
        """
        Get the group ID for a specific client
        
        Args:
            client_id: Client identifier
        
        Returns:
            Group ID
        """
        for group_id, client_ids in self.client_groups.items():
            if client_id in client_ids:
                return group_id
        return None
    
    def get_similar_clients(self, client_id):
        """
        Get list of clients similar to the given client
        
        Args:
            client_id: Client identifier
        
        Returns:
            List of similar client IDs
        """
        group_id = self.get_client_group(client_id)
        if group_id is not None:
            return [cid for cid in self.client_groups[group_id] if cid != client_id]
        return []


def create_federated_setup(client_configs, n_clusters=3, random_state=None):
    """
    Create a federated setup with multiple clients and a server
    
    Args:
        client_configs: List of client configuration dictionaries
        n_clusters: Number of clusters for client grouping
        random_state: Random seed
    
    Returns:
        server: FederatedServer instance
        clients: Dictionary of FederatedClient instances
    """
    # Create server
    server = FederatedServer(n_clusters=n_clusters, random_state=random_state)
    
    # Create clients
    clients = {}
    for config in client_configs:
        client = FederatedClient(**config)
        clients[client.client_id] = client
        server.register_client(client)
    
    return server, clients
